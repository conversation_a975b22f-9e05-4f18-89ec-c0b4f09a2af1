-- 学生活动申请系统数据库初始化脚本
-- MySQL 8.0.42

-- 创建数据库
CREATE DATABASE IF NOT EXISTS activity_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE activity_system;

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    student_id VARCHAR(20) COMMENT '学号',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    role ENUM('student', 'admin') DEFAULT 'student' COMMENT '角色',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_username (username),
    INDEX idx_student_id (student_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 活动申请表
CREATE TABLE activity_applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '申请人ID',
    activity_name VARCHAR(200) NOT NULL COMMENT '活动名称',
    activity_type ENUM('班会', '读书会', '学术讲座', '文艺演出', '体育活动', '社团活动', '志愿服务', '其他') NOT NULL COMMENT '活动类型',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    location VARCHAR(200) NOT NULL COMMENT '地点',
    budget DECIMAL(10,2) DEFAULT 0.00 COMMENT '预算',
    expected_participants INT NOT NULL COMMENT '预计参与人数',
    need_venue BOOLEAN DEFAULT FALSE COMMENT '是否需要特殊场地',
    need_equipment BOOLEAN DEFAULT FALSE COMMENT '是否需要设备',
    description TEXT COMMENT '活动描述',
    status ENUM('pending', 'approved', 'rejected', 'auto_approved') DEFAULT 'pending' COMMENT '状态',
    admin_comment TEXT COMMENT '管理员备注',
    apply_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    review_time TIMESTAMP NULL COMMENT '审批时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_apply_time (apply_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动申请表';

-- 插入默认管理员账户
INSERT INTO users (username, password, real_name, role) VALUES 
('admin', 'admin123', '系统管理员', 'admin');

-- 插入测试学生账户
INSERT INTO users (username, password, real_name, student_id, email, phone, role) VALUES 
('student1', '123456', '张三', '2021001', '<EMAIL>', '13800138001', 'student'),
('student2', '123456', '李四', '2021002', '<EMAIL>', '13800138002', 'student');

-- 插入测试活动申请数据
INSERT INTO activity_applications (user_id, activity_name, activity_type, start_time, end_time, location, budget, expected_participants, need_venue, need_equipment, description, status) VALUES 
(2, '班级读书分享会', '读书会', '2024-01-15 14:00:00', '2024-01-15 16:00:00', '教室A101', 0.00, 30, FALSE, FALSE, '班级内部读书分享活动，分享最近阅读的好书', 'auto_approved'),
(3, '新年文艺晚会', '文艺演出', '2024-01-20 19:00:00', '2024-01-20 21:00:00', '大礼堂', 5000.00, 200, TRUE, TRUE, '庆祝新年的大型文艺演出活动', 'pending');
