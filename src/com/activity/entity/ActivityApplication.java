package com.activity.entity;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 活动申请实体类
 */
public class ActivityApplication {
    private int id;
    private int userId;
    private String activityName;
    private String activityType;
    private Timestamp startTime;
    private Timestamp endTime;
    private String location;
    private BigDecimal budget;
    private int expectedParticipants;
    private boolean needVenue;
    private boolean needEquipment;
    private String description;
    private String status;
    private String adminComment;
    private Timestamp applyTime;
    private Timestamp reviewTime;
    
    // 关联用户信息（用于显示）
    private String applicantName;
    private String applicantStudentId;

    // 构造方法
    public ActivityApplication() {}

    public ActivityApplication(int userId, String activityName, String activityType,
                             Timestamp startTime, Timestamp endTime, String location,
                             BigDecimal budget, int expectedParticipants,
                             boolean needVenue, boolean needEquipment, String description) {
        this.userId = userId;
        this.activityName = activityName;
        this.activityType = activityType;
        this.startTime = startTime;
        this.endTime = endTime;
        this.location = location;
        this.budget = budget;
        this.expectedParticipants = expectedParticipants;
        this.needVenue = needVenue;
        this.needEquipment = needEquipment;
        this.description = description;
        this.status = "pending";
    }

    // Getter和Setter方法
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public BigDecimal getBudget() {
        return budget;
    }

    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    public int getExpectedParticipants() {
        return expectedParticipants;
    }

    public void setExpectedParticipants(int expectedParticipants) {
        this.expectedParticipants = expectedParticipants;
    }

    public boolean isNeedVenue() {
        return needVenue;
    }

    public void setNeedVenue(boolean needVenue) {
        this.needVenue = needVenue;
    }

    public boolean isNeedEquipment() {
        return needEquipment;
    }

    public void setNeedEquipment(boolean needEquipment) {
        this.needEquipment = needEquipment;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAdminComment() {
        return adminComment;
    }

    public void setAdminComment(String adminComment) {
        this.adminComment = adminComment;
    }

    public Timestamp getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Timestamp applyTime) {
        this.applyTime = applyTime;
    }

    public Timestamp getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Timestamp reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getApplicantStudentId() {
        return applicantStudentId;
    }

    public void setApplicantStudentId(String applicantStudentId) {
        this.applicantStudentId = applicantStudentId;
    }

    /**
     * 判断是否符合自动批准条件
     */
    public boolean isAutoApprovalEligible() {
        // 自动批准条件：
        // 1. 预计人数 <= 50
        // 2. 预算 = 0
        // 3. 活动类型为常规类型（班会、读书会）
        // 4. 不需要特殊场地
        // 5. 不需要设备
        return expectedParticipants <= 50 
               && (budget == null || budget.compareTo(BigDecimal.ZERO) == 0)
               && ("班会".equals(activityType) || "读书会".equals(activityType))
               && !needVenue 
               && !needEquipment;
    }

    @Override
    public String toString() {
        return "ActivityApplication{" +
                "id=" + id +
                ", userId=" + userId +
                ", activityName='" + activityName + '\'' +
                ", activityType='" + activityType + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", location='" + location + '\'' +
                ", budget=" + budget +
                ", expectedParticipants=" + expectedParticipants +
                ", needVenue=" + needVenue +
                ", needEquipment=" + needEquipment +
                ", status='" + status + '\'' +
                ", applyTime=" + applyTime +
                '}';
    }
}
